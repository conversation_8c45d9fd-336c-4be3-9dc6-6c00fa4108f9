<?php
/**
 * YouTube Live to M3U8 Converter - Versión Simplificada
 * Funciona sin yt-dlp usando APIs públicas de YouTube
 */

header('Content-Type: application/vnd.apple.mpegurl');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Configuración
define('CACHE_DIR', __DIR__ . '/cache/');
define('CACHE_DURATION', 300); // 5 minutos
define('LOG_FILE', __DIR__ . '/yutu_simple.log');

// Crear directorio de cache si no existe
if (!is_dir(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

/**
 * Función para logging
 */
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents(LOG_FILE, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

/**
 * Extraer video ID de URL de YouTube
 */
function extractVideoId($url) {
    $pattern = '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/';
    preg_match($pattern, $url, $matches);
    return isset($matches[1]) ? $matches[1] : false;
}

/**
 * Obtener información del video usando API pública
 */
function getVideoInfo($videoId) {
    $cacheKey = md5($videoId);
    $cacheFile = CACHE_DIR . $cacheKey . '.json';
    
    // Verificar cache
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < CACHE_DURATION) {
        $cached = file_get_contents($cacheFile);
        return json_decode($cached, true);
    }
    
    // Usar múltiples métodos para obtener información del video
    $info = null;
    
    // Método 1: YouTube oEmbed API
    $oembedUrl = "https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v={$videoId}&format=json";
    $oembedData = fetchUrl($oembedUrl);
    
    if ($oembedData) {
        $oembed = json_decode($oembedData, true);
        if ($oembed && isset($oembed['title'])) {
            $info = [
                'title' => $oembed['title'],
                'author' => $oembed['author_name'] ?? 'Unknown',
                'thumbnail' => $oembed['thumbnail_url'] ?? '',
                'video_id' => $videoId
            ];
        }
    }
    
    // Método 2: Intentar obtener streams directamente
    $streamUrls = getStreamUrls($videoId);
    if ($streamUrls) {
        if (!$info) {
            $info = [
                'title' => 'YouTube Video',
                'author' => 'Unknown',
                'thumbnail' => '',
                'video_id' => $videoId
            ];
        }
        $info['streams'] = $streamUrls;
    }
    
    if ($info) {
        // Guardar en cache
        file_put_contents($cacheFile, json_encode($info));
        return $info;
    }
    
    return false;
}

/**
 * Obtener URLs de stream usando métodos alternativos
 */
function getStreamUrls($videoId) {
    // Intentar obtener información del player de YouTube
    $playerUrl = "https://www.youtube.com/watch?v={$videoId}";
    $pageContent = fetchUrl($playerUrl);
    
    if (!$pageContent) {
        return false;
    }
    
    // Buscar configuración del player
    $patterns = [
        '/ytInitialPlayerResponse\s*=\s*({.+?});/',
        '/"streamingData":\s*({.+?})/',
        '/hlsManifestUrl":"([^"]+)"/',
        '/dashManifestUrl":"([^"]+)"/'
    ];
    
    $streams = [];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $pageContent, $matches)) {
            if (strpos($pattern, 'hlsManifestUrl') !== false) {
                $hlsUrl = json_decode('"' . $matches[1] . '"'); // Decodificar caracteres escapados
                if ($hlsUrl) {
                    $streams['hls'] = $hlsUrl;
                }
            } elseif (strpos($pattern, 'dashManifestUrl') !== false) {
                $dashUrl = json_decode('"' . $matches[1] . '"');
                if ($dashUrl) {
                    $streams['dash'] = $dashUrl;
                }
            }
        }
    }
    
    return !empty($streams) ? $streams : false;
}

/**
 * Realizar petición HTTP
 */
function fetchUrl($url) {
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language: en-US,en;q=0.5',
                'Accept-Encoding: gzip, deflate',
                'Connection: keep-alive'
            ],
            'timeout' => 30
        ]
    ]);
    
    $result = @file_get_contents($url, false, $context);
    
    if ($result === false && function_exists('curl_init')) {
        // Fallback a cURL si file_get_contents falla
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $result = curl_exec($ch);
        curl_close($ch);
    }
    
    return $result;
}

/**
 * Generar M3U8 desde información del video
 */
function generateM3U8($videoInfo) {
    $m3u8Content = "#EXTM3U\n";
    $m3u8Content .= "#EXT-X-VERSION:3\n";
    $m3u8Content .= "#EXT-X-TARGETDURATION:10\n";
    $m3u8Content .= "#EXT-X-MEDIA-SEQUENCE:0\n";
    
    $title = $videoInfo['title'] ?? 'YouTube Video';
    
    // Si tenemos streams directos, usarlos
    if (isset($videoInfo['streams'])) {
        if (isset($videoInfo['streams']['hls'])) {
            // Redirigir al HLS nativo de YouTube
            header('Location: ' . $videoInfo['streams']['hls']);
            exit;
        }
    }
    
    // Fallback: generar M3U8 básico con enlace al video
    $videoUrl = "https://www.youtube.com/watch?v=" . $videoInfo['video_id'];
    
    $m3u8Content .= "#EXTINF:3600,$title\n";
    $m3u8Content .= "$videoUrl\n";
    $m3u8Content .= "#EXT-X-ENDLIST\n";
    
    return $m3u8Content;
}

/**
 * Función principal
 */
function main() {
    try {
        // Verificar si se proporcionó URL
        if (!isset($_GET['url']) || empty($_GET['url'])) {
            throw new Exception("Parámetro 'url' requerido");
        }
        
        $url = $_GET['url'];
        $videoId = extractVideoId($url);
        
        if (!$videoId) {
            throw new Exception("URL de YouTube no válida");
        }
        
        logMessage("Procesando video ID: $videoId");
        
        // Obtener información del video
        $videoInfo = getVideoInfo($videoId);
        
        if (!$videoInfo) {
            throw new Exception("No se pudo obtener información del video");
        }
        
        // Generar M3U8
        $m3u8 = generateM3U8($videoInfo);
        
        logMessage("M3U8 generado exitosamente para: $videoId");
        
        // Enviar respuesta
        echo $m3u8;
        
    } catch (Exception $e) {
        logMessage("Error: " . $e->getMessage());
        
        // Enviar error como M3U8 válido pero vacío
        header('HTTP/1.1 500 Internal Server Error');
        echo "#EXTM3U\n";
        echo "#EXT-X-VERSION:3\n";
        echo "#EXTINF:0,Error: " . $e->getMessage() . "\n";
        echo "#EXT-X-ENDLIST\n";
    }
}

/**
 * Función de debug
 */
function debugInfo() {
    echo "<h2>Debug Information - Versión Simplificada</h2>";
    echo "<h3>PHP Info:</h3>";
    echo "PHP Version: " . PHP_VERSION . "<br>";
    echo "file_get_contents enabled: " . (ini_get('allow_url_fopen') ? 'YES' : 'NO') . "<br>";
    echo "curl enabled: " . (function_exists('curl_init') ? 'YES' : 'NO') . "<br>";
    echo "Current directory: " . __DIR__ . "<br>";
    echo "Cache directory: " . CACHE_DIR . "<br>";
    echo "Cache writable: " . (is_writable(CACHE_DIR) ? 'YES' : 'NO') . "<br>";
    
    echo "<h3>Test URL fetch:</h3>";
    $testUrl = 'https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ&format=json';
    $result = fetchUrl($testUrl);
    echo "Test fetch result: " . ($result ? 'SUCCESS' : 'FAILED') . "<br>";
    if ($result) {
        echo "Sample data: " . substr($result, 0, 200) . "...<br>";
    }
    
    echo "<h3>Recent Logs:</h3>";
    if (file_exists(LOG_FILE)) {
        $logs = file_get_contents(LOG_FILE);
        echo "<pre>" . htmlspecialchars(substr($logs, -1000)) . "</pre>";
    } else {
        echo "No log file found<br>";
    }
}

// Ejecutar
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    if (isset($_GET['debug']) && $_GET['debug'] == '1') {
        debugInfo();
    } else {
        main();
    }
}
?>
