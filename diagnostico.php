<?php
/**
 * Script de Diagnóstico para YouTube to M3U8 Converter
 * Verifica la configuración del servidor y diagnostica problemas
 */

header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico YouTube to M3U8</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .success { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .error { color: red; background: #ffe8e8; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        .test-url { background: #f9f9f9; padding: 10px; border-radius: 5px; font-family: monospace; word-break: break-all; }
    </style>
</head>
<body>
    <h1>🔍 Diagnóstico YouTube to M3U8 Converter</h1>
    
    <?php
    
    function checkStatus($condition, $message) {
        if ($condition) {
            echo "<div class='success'>✅ $message</div>";
            return true;
        } else {
            echo "<div class='error'>❌ $message</div>";
            return false;
        }
    }
    
    function checkWarning($condition, $message) {
        if ($condition) {
            echo "<div class='success'>✅ $message</div>";
            return true;
        } else {
            echo "<div class='warning'>⚠️ $message</div>";
            return false;
        }
    }
    
    echo '<div class="section">';
    echo '<h2>📋 Información del Servidor</h2>';
    echo "Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
    echo "PHP Version: " . PHP_VERSION . "<br>";
    echo "Directorio actual: " . __DIR__ . "<br>";
    echo "URL actual: " . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . "<br>";
    echo '</div>';
    
    echo '<div class="section">';
    echo '<h2>🔧 Verificación de Requisitos PHP</h2>';
    
    $phpOk = checkStatus(version_compare(PHP_VERSION, '7.0.0', '>='), "PHP 7.0+ (actual: " . PHP_VERSION . ")");
    $jsonOk = checkStatus(function_exists('json_decode'), "Extensión JSON");
    $curlOk = checkWarning(function_exists('curl_init'), "Extensión cURL");
    $fopenOk = checkWarning(ini_get('allow_url_fopen'), "allow_url_fopen habilitado");
    $shellOk = checkWarning(function_exists('shell_exec'), "shell_exec habilitado");
    $execOk = checkWarning(function_exists('exec'), "exec habilitado");
    
    if (!$curlOk && !$fopenOk) {
        echo "<div class='error'>❌ CRÍTICO: Necesitas al menos cURL o allow_url_fopen para hacer peticiones HTTP</div>";
    }
    
    echo '</div>';
    
    echo '<div class="section">';
    echo '<h2>📁 Verificación de Archivos y Permisos</h2>';
    
    $files = ['yutu.php', 'yutu_simple.php', 'install.php'];
    foreach ($files as $file) {
        $exists = file_exists(__DIR__ . '/' . $file);
        checkStatus($exists, "Archivo $file existe");
        if ($exists) {
            $readable = is_readable(__DIR__ . '/' . $file);
            checkStatus($readable, "Archivo $file es legible");
        }
    }
    
    $cacheDir = __DIR__ . '/cache';
    $cacheDirExists = is_dir($cacheDir);
    checkStatus($cacheDirExists, "Directorio cache existe");
    
    if (!$cacheDirExists) {
        $created = @mkdir($cacheDir, 0755, true);
        checkStatus($created, "Directorio cache creado");
        $cacheDirExists = $created;
    }
    
    if ($cacheDirExists) {
        $writable = is_writable($cacheDir);
        checkStatus($writable, "Directorio cache es escribible");
    }
    
    $logWritable = is_writable(__DIR__);
    checkStatus($logWritable, "Directorio principal es escribible (para logs)");
    
    echo '</div>';
    
    echo '<div class="section">';
    echo '<h2>🌐 Test de Conectividad</h2>';
    
    // Test básico de conectividad
    $testUrls = [
        'https://www.youtube.com' => 'YouTube principal',
        'https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ&format=json' => 'YouTube oEmbed API'
    ];
    
    foreach ($testUrls as $url => $description) {
        echo "<h4>Probando: $description</h4>";
        
        $success = false;
        $method = '';
        $result = '';
        
        // Probar con cURL
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; YT-M3U8-Converter)');
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($result !== false && $httpCode == 200) {
                $success = true;
                $method = 'cURL';
            } else {
                echo "<div class='error'>cURL falló: HTTP $httpCode - $error</div>";
            }
        }
        
        // Probar con file_get_contents si cURL falló
        if (!$success && ini_get('allow_url_fopen')) {
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => 'User-Agent: Mozilla/5.0 (compatible; YT-M3U8-Converter)',
                    'timeout' => 10
                ]
            ]);
            
            $result = @file_get_contents($url, false, $context);
            
            if ($result !== false) {
                $success = true;
                $method = 'file_get_contents';
            } else {
                echo "<div class='error'>file_get_contents falló</div>";
            }
        }
        
        if ($success) {
            echo "<div class='success'>✅ Conectividad OK usando $method</div>";
            if (strpos($url, 'oembed') !== false) {
                $json = json_decode($result, true);
                if ($json && isset($json['title'])) {
                    echo "<div class='info'>Datos recibidos: " . htmlspecialchars($json['title']) . "</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ No se pudo conectar con ningún método</div>";
        }
    }
    
    echo '</div>';
    
    echo '<div class="section">';
    echo '<h2>🧪 Test de Scripts</h2>';
    
    $baseUrl = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    
    echo '<h4>URLs de prueba:</h4>';
    
    $testVideoUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
    
    $testUrls = [
        'yutu.php?debug=1' => 'Debug del script principal',
        'yutu_simple.php?debug=1' => 'Debug del script simplificado',
        'yutu.php?url=' . urlencode($testVideoUrl) => 'Script principal con video de prueba',
        'yutu_simple.php?url=' . urlencode($testVideoUrl) => 'Script simplificado con video de prueba'
    ];
    
    foreach ($testUrls as $path => $description) {
        $fullUrl = $baseUrl . '/' . $path;
        echo "<div class='test-url'>";
        echo "<strong>$description:</strong><br>";
        echo "<a href='$fullUrl' target='_blank'>$fullUrl</a>";
        echo "</div>";
    }
    
    echo '</div>';
    
    echo '<div class="section">';
    echo '<h2>📝 Logs Recientes</h2>';
    
    $logFiles = ['yutu.log', 'yutu_simple.log'];
    
    foreach ($logFiles as $logFile) {
        $logPath = __DIR__ . '/' . $logFile;
        if (file_exists($logPath)) {
            echo "<h4>$logFile:</h4>";
            $logs = file_get_contents($logPath);
            echo "<pre>" . htmlspecialchars(substr($logs, -1000)) . "</pre>";
        } else {
            echo "<div class='info'>No se encontró $logFile</div>";
        }
    }
    
    echo '</div>';
    
    echo '<div class="section">';
    echo '<h2>💡 Recomendaciones</h2>';
    
    if (!$curlOk && !$fopenOk) {
        echo "<div class='error'>🚨 CRÍTICO: Contacta a tu proveedor de hosting para habilitar cURL o allow_url_fopen</div>";
    }
    
    if (!$shellOk) {
        echo "<div class='warning'>⚠️ shell_exec está deshabilitado. Usa yutu_simple.php en lugar de yutu.php</div>";
    }
    
    echo "<div class='info'>💡 Si yutu.php no funciona, prueba yutu_simple.php que no requiere yt-dlp</div>";
    echo "<div class='info'>💡 Revisa los logs para ver errores específicos</div>";
    echo "<div class='info'>💡 Asegúrate de que las URLs de YouTube sean válidas y públicas</div>";
    
    echo '</div>';
    
    ?>
    
    <div class="section">
        <h2>🔄 Acciones</h2>
        <button onclick="location.reload()">🔄 Actualizar Diagnóstico</button>
        <button onclick="window.open('install.php', '_blank')">⚙️ Ir al Instalador</button>
        <button onclick="window.open('ejemplo.html', '_blank')">🎬 Ir al Ejemplo</button>
    </div>
    
</body>
</html>
