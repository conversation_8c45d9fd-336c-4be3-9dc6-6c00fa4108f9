<?php
/**
 * YouTube Live to M3U8 Converter
 * Convierte streams en vivo de YouTube a formato M3U8
 * 
 * Uso: yutu.php?url=https://www.youtube.com/watch?v=VIDEO_ID
 * Ejemplo: yutu.php?url=https://www.youtube.com/watch?v=gOJvu0xYsdo
 */

header('Content-Type: application/vnd.apple.mpegurl');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Configuración
define('YT_DLP_PATH', '/usr/local/bin/yt-dlp'); // Ajusta la ruta según tu servidor
define('CACHE_DIR', __DIR__ . '/cache/');
define('CACHE_DURATION', 300); // 5 minutos
define('LOG_FILE', __DIR__ . '/yutu.log');

// Crear directorio de cache si no existe
if (!is_dir(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

/**
 * Función para logging
 */
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents(LOG_FILE, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

/**
 * Función para autoactualizarse
 */
function autoUpdate() {
    try {
        // Actualizar yt-dlp
        $updateCmd = YT_DLP_PATH . ' -U 2>&1';
        $output = shell_exec($updateCmd);
        logMessage("Auto-update yt-dlp: " . $output);
        
        // También puedes agregar aquí lógica para actualizar este mismo script
        // desde un repositorio remoto si lo deseas
        
        return true;
    } catch (Exception $e) {
        logMessage("Error en auto-update: " . $e->getMessage());
        return false;
    }
}

/**
 * Función para obtener información del stream
 */
function getStreamInfo($url) {
    $cacheKey = md5($url);
    $cacheFile = CACHE_DIR . $cacheKey . '.json';
    
    // Verificar cache
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < CACHE_DURATION) {
        $cached = file_get_contents($cacheFile);
        return json_decode($cached, true);
    }
    
    // Comando para obtener información del stream
    $cmd = YT_DLP_PATH . ' --no-warnings --dump-json --format "best[ext=mp4]/best" ' . escapeshellarg($url) . ' 2>&1';
    
    logMessage("Ejecutando comando: $cmd");
    $output = shell_exec($cmd);
    
    if (empty($output)) {
        throw new Exception("No se pudo obtener información del video");
    }
    
    $info = json_decode($output, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Error al parsear JSON: " . $output);
    }
    
    // Guardar en cache
    file_put_contents($cacheFile, $output);
    
    return $info;
}

/**
 * Función para generar M3U8
 */
function generateM3U8($streamInfo) {
    $m3u8Content = "#EXTM3U\n";
    $m3u8Content .= "#EXT-X-VERSION:3\n";
    $m3u8Content .= "#EXT-X-TARGETDURATION:10\n";
    $m3u8Content .= "#EXT-X-MEDIA-SEQUENCE:0\n";
    
    // Información del stream
    $title = isset($streamInfo['title']) ? $streamInfo['title'] : 'YouTube Live Stream';
    $duration = isset($streamInfo['duration']) ? $streamInfo['duration'] : 0;
    
    $m3u8Content .= "#EXTINF:$duration,$title\n";
    
    // URL del stream
    if (isset($streamInfo['url'])) {
        $m3u8Content .= $streamInfo['url'] . "\n";
    } elseif (isset($streamInfo['formats'])) {
        // Buscar el mejor formato
        $bestFormat = null;
        foreach ($streamInfo['formats'] as $format) {
            if (isset($format['url']) && 
                (strpos($format['format_id'], 'hls') !== false || 
                 strpos($format['ext'], 'mp4') !== false)) {
                $bestFormat = $format;
                break;
            }
        }
        
        if ($bestFormat && isset($bestFormat['url'])) {
            $m3u8Content .= $bestFormat['url'] . "\n";
        } else {
            throw new Exception("No se encontró un formato de stream válido");
        }
    } else {
        throw new Exception("No se encontró URL de stream");
    }
    
    $m3u8Content .= "#EXT-X-ENDLIST\n";
    
    return $m3u8Content;
}

/**
 * Función principal
 */
function main() {
    try {
        // Verificar si se proporcionó URL
        if (!isset($_GET['url']) || empty($_GET['url'])) {
            throw new Exception("Parámetro 'url' requerido");
        }

        $url = $_GET['url'];

        // Validar que sea una URL de YouTube
        if (!preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $url)) {
            throw new Exception("URL de YouTube no válida");
        }

        logMessage("Procesando URL: $url");

        // Auto-actualizar si se solicita
        if (isset($_GET['update']) && $_GET['update'] == '1') {
            autoUpdate();
        }

        // Obtener información del stream
        $streamInfo = getStreamInfo($url);

        // Generar M3U8
        $m3u8 = generateM3U8($streamInfo);

        logMessage("M3U8 generado exitosamente para: $url");

        // Enviar respuesta
        echo $m3u8;

    } catch (Exception $e) {
        logMessage("Error: " . $e->getMessage());

        // Enviar error como M3U8 válido pero vacío
        header('HTTP/1.1 500 Internal Server Error');
        echo "#EXTM3U\n";
        echo "#EXT-X-VERSION:3\n";
        echo "#EXTINF:0,Error: " . $e->getMessage() . "\n";
        echo "#EXT-X-ENDLIST\n";
    }
}

/**
 * Función de debug para mostrar información del sistema
 */
function debugInfo() {
    echo "<h2>Debug Information</h2>";
    echo "<h3>PHP Info:</h3>";
    echo "PHP Version: " . PHP_VERSION . "<br>";
    echo "shell_exec enabled: " . (function_exists('shell_exec') ? 'YES' : 'NO') . "<br>";
    echo "curl enabled: " . (function_exists('curl_init') ? 'YES' : 'NO') . "<br>";
    echo "Current directory: " . __DIR__ . "<br>";
    echo "YT-DLP Path: " . YT_DLP_PATH . "<br>";
    echo "YT-DLP exists: " . (file_exists(YT_DLP_PATH) ? 'YES' : 'NO') . "<br>";
    echo "YT-DLP executable: " . (is_executable(YT_DLP_PATH) ? 'YES' : 'NO') . "<br>";

    echo "<h3>Test shell_exec:</h3>";
    $testCmd = 'echo "test"';
    $result = shell_exec($testCmd);
    echo "Test command result: " . ($result ? $result : 'FAILED') . "<br>";

    if (file_exists(YT_DLP_PATH)) {
        echo "<h3>YT-DLP Version:</h3>";
        $versionCmd = YT_DLP_PATH . ' --version 2>&1';
        $version = shell_exec($versionCmd);
        echo "Version: " . ($version ? $version : 'FAILED') . "<br>";
    }

    echo "<h3>Recent Logs:</h3>";
    if (file_exists(LOG_FILE)) {
        $logs = file_get_contents(LOG_FILE);
        echo "<pre>" . htmlspecialchars(substr($logs, -1000)) . "</pre>";
    } else {
        echo "No log file found<br>";
    }
}

// Ejecutar solo si se accede directamente
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    // Si se solicita debug, mostrar información del sistema
    if (isset($_GET['debug']) && $_GET['debug'] == '1') {
        debugInfo();
    } else {
        main();
    }
}
?>
