<?php
/**
 * Instalador automático para YouTube to M3U8 Converter
 * Este script instala y configura automáticamente yt-dlp en tu hosting
 */

header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalador YouTube to M3U8</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .success { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; }
        .error { color: red; background: #ffe8e8; padding: 10px; border-radius: 5px; }
        .info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 5px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>🎥 Instalador YouTube to M3U8 Converter</h1>
    
    <?php
    
    function checkRequirements() {
        $results = [];
        
        // Verificar PHP
        $results['php'] = version_compare(PHP_VERSION, '7.4.0', '>=');
        
        // Verificar funciones necesarias
        $results['shell_exec'] = function_exists('shell_exec');
        $results['curl'] = function_exists('curl_init');
        $results['json'] = function_exists('json_decode');
        
        // Verificar permisos de escritura
        $results['write_permission'] = is_writable(__DIR__);
        
        return $results;
    }
    
    function downloadYtDlp() {
        $ytDlpPath = __DIR__ . '/yt-dlp';
        
        // URL de descarga de yt-dlp
        $downloadUrl = 'https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp';
        
        // Descargar yt-dlp
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $downloadUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; YT-DLP-Installer)');
        
        $data = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200 && $data !== false) {
            file_put_contents($ytDlpPath, $data);
            chmod($ytDlpPath, 0755);
            return true;
        }
        
        return false;
    }
    
    function updateYutuConfig() {
        $yutuPath = __DIR__ . '/yutu.php';
        $ytDlpPath = __DIR__ . '/yt-dlp';
        
        if (file_exists($yutuPath)) {
            $content = file_get_contents($yutuPath);
            $content = str_replace(
                "define('YT_DLP_PATH', '/usr/local/bin/yt-dlp');",
                "define('YT_DLP_PATH', '$ytDlpPath');",
                $content
            );
            file_put_contents($yutuPath, $content);
            return true;
        }
        
        return false;
    }
    
    if (isset($_POST['install'])) {
        echo '<div class="step">';
        echo '<h3>🔧 Iniciando instalación...</h3>';
        
        // Verificar requisitos
        $requirements = checkRequirements();
        $canInstall = true;
        
        foreach ($requirements as $req => $status) {
            if ($status) {
                echo "<div class='success'>✅ $req: OK</div>";
            } else {
                echo "<div class='error'>❌ $req: FALLO</div>";
                $canInstall = false;
            }
        }
        
        if (!$canInstall) {
            echo '<div class="error">❌ No se pueden cumplir todos los requisitos. Contacta a tu proveedor de hosting.</div>';
        } else {
            echo '<h4>📥 Descargando yt-dlp...</h4>';
            
            if (downloadYtDlp()) {
                echo '<div class="success">✅ yt-dlp descargado exitosamente</div>';
                
                echo '<h4>⚙️ Configurando yutu.php...</h4>';
                if (updateYutuConfig()) {
                    echo '<div class="success">✅ Configuración actualizada</div>';
                } else {
                    echo '<div class="error">❌ Error al actualizar configuración</div>';
                }
                
                // Crear directorio de cache
                $cacheDir = __DIR__ . '/cache';
                if (!is_dir($cacheDir)) {
                    mkdir($cacheDir, 0755, true);
                    echo '<div class="success">✅ Directorio de cache creado</div>';
                }
                
                echo '<div class="success">';
                echo '<h3>🎉 ¡Instalación completada!</h3>';
                echo '<p>Tu script está listo para usar. Puedes probarlo con:</p>';
                echo '<pre>' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . 'yutu.php?url=https://www.youtube.com/watch?v=gOJvu0xYsdo</pre>';
                echo '</div>';
                
            } else {
                echo '<div class="error">❌ Error al descargar yt-dlp</div>';
            }
        }
        
        echo '</div>';
    } else {
        // Mostrar formulario de instalación
        $requirements = checkRequirements();
        
        echo '<div class="step">';
        echo '<h3>📋 Verificación de requisitos</h3>';
        
        foreach ($requirements as $req => $status) {
            if ($status) {
                echo "<div class='success'>✅ $req: OK</div>";
            } else {
                echo "<div class='error'>❌ $req: FALLO</div>";
            }
        }
        echo '</div>';
        
        echo '<div class="step">';
        echo '<h3>🚀 Instalación</h3>';
        echo '<p>Este instalador descargará e instalará automáticamente yt-dlp y configurará el script.</p>';
        echo '<form method="post">';
        echo '<button type="submit" name="install">Instalar Ahora</button>';
        echo '</form>';
        echo '</div>';
        
        echo '<div class="step">';
        echo '<h3>📖 Instrucciones de uso</h3>';
        echo '<p>Una vez instalado, podrás usar el script así:</p>';
        echo '<pre>https://tudominio.com/ruta/yutu.php?url=https://www.youtube.com/watch?v=VIDEO_ID</pre>';
        echo '<p>Parámetros adicionales:</p>';
        echo '<ul>';
        echo '<li><code>update=1</code> - Actualiza yt-dlp automáticamente</li>';
        echo '</ul>';
        echo '</div>';
    }
    ?>
    
</body>
</html>
