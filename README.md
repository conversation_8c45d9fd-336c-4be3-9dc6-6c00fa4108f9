# 🎥 YouTube Live to M3U8 Converter

Un script PHP que convierte streams en vivo de YouTube a formato M3U8 para su uso en reproductores de video.

## 🚀 Características

- ✅ Convierte streams de YouTube a formato M3U8
- ✅ Auto-actualización de yt-dlp
- ✅ Sistema de cache para mejor rendimiento
- ✅ Instalación automática
- ✅ Compatible con la mayoría de hostings
- ✅ Logs detallados para debugging
- ✅ CORS habilitado para uso desde cualquier dominio

## 📦 Instalación

### Opción 1: Instalación Automática (Recomendada)

1. Sube todos los archivos a tu hosting
2. Visita `https://tudominio.com/ruta/install.php`
3. Sigue las instrucciones en pantalla
4. ¡Listo para usar!

### Opción 2: Instalación Manual

1. Asegúrate de tener yt-dlp instalado en tu servidor
2. Edita `yutu.php` y ajusta la ruta de `YT_DLP_PATH`
3. Crea el directorio `cache/` con permisos de escritura

## 🎯 Uso

### Uso Básico
```
https://tudominio.com/ruta/yutu.php?url=https://www.youtube.com/watch?v=VIDEO_ID
```

### Ejemplo Real
```
https://rogsmediatv.xyz/mamones/yutu/yutu.php?url=https://www.youtube.com/watch?v=gOJvu0xYsdo
```

### Parámetros Adicionales

- `update=1` - Actualiza yt-dlp antes de procesar
  ```
  yutu.php?url=VIDEO_URL&update=1
  ```

## 🔧 Configuración

### Configuración en yutu.php

```php
define('YT_DLP_PATH', '/ruta/a/yt-dlp');    // Ruta al ejecutable yt-dlp
define('CACHE_DURATION', 300);              // Duración del cache en segundos
define('LOG_FILE', __DIR__ . '/yutu.log');  // Archivo de logs
```

## 📱 Uso en Reproductores

### HTML5 Video
```html
<video controls>
    <source src="https://tudominio.com/yutu.php?url=YOUTUBE_URL" type="application/x-mpegURL">
</video>
```

### Video.js
```javascript
var player = videojs('my-video');
player.src({
    src: 'https://tudominio.com/yutu.php?url=YOUTUBE_URL',
    type: 'application/x-mpegURL'
});
```

### HLS.js
```javascript
var video = document.getElementById('video');
var hls = new Hls();
hls.loadSource('https://tudominio.com/yutu.php?url=YOUTUBE_URL');
hls.attachMedia(video);
```

## 🛠️ Requisitos del Servidor

- PHP 7.4 o superior
- Función `shell_exec()` habilitada
- Extensión cURL habilitada
- Permisos de escritura en el directorio del script
- Acceso para descargar archivos externos

## 📁 Estructura de Archivos

```
/
├── yutu.php          # Script principal
├── install.php       # Instalador automático
├── README.md         # Este archivo
├── cache/            # Directorio de cache (se crea automáticamente)
├── yt-dlp            # Ejecutable yt-dlp (se descarga automáticamente)
└── yutu.log          # Archivo de logs
```

## 🔍 Debugging

### Verificar Logs
Los logs se guardan en `yutu.log`. Puedes verificarlos para diagnosticar problemas:

```bash
tail -f yutu.log
```

### Errores Comunes

1. **"No se pudo obtener información del video"**
   - Verifica que la URL de YouTube sea válida
   - Asegúrate de que yt-dlp esté actualizado

2. **"Permission denied"**
   - Verifica permisos de escritura en el directorio
   - Asegúrate de que yt-dlp tenga permisos de ejecución

3. **"shell_exec() disabled"**
   - Contacta a tu proveedor de hosting para habilitar shell_exec()

## 🔄 Actualización

Para actualizar yt-dlp automáticamente, agrega `&update=1` a tu URL:

```
yutu.php?url=YOUTUBE_URL&update=1
```

## ⚠️ Consideraciones Legales

- Este script es para uso educativo y personal
- Respeta los términos de servicio de YouTube
- No uses para contenido con derechos de autor sin permiso
- El uso comercial puede requerir licencias adicionales

## 🤝 Soporte

Si encuentras problemas:

1. Verifica los logs en `yutu.log`
2. Asegúrate de que todos los requisitos estén cumplidos
3. Prueba con diferentes URLs de YouTube
4. Verifica que yt-dlp esté actualizado

## 📄 Licencia

Este proyecto es de código abierto. Úsalo bajo tu propia responsabilidad.
