<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Live to M3U8 - Ejemplo</title>
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .video-container {
            margin: 20px 0;
            text-align: center;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        button {
            background: #ff0000;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        button:hover {
            background: #cc0000;
        }
        
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .url-display {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .examples {
            margin: 20px 0;
        }
        
        .example-item {
            background: #f9f9f9;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 YouTube Live to M3U8 Converter</h1>
        <p>Convierte streams en vivo de YouTube a formato M3U8 para usar en reproductores de video.</p>
        
        <div class="form-group">
            <label for="youtube-url">URL de YouTube:</label>
            <input type="url" id="youtube-url" placeholder="https://www.youtube.com/watch?v=..." 
                   value="https://www.youtube.com/watch?v=gOJvu0xYsdo">
        </div>
        
        <div class="form-group">
            <button onclick="loadVideo()">🎬 Cargar Video</button>
            <button onclick="generateM3U8()">📋 Generar M3U8</button>
            <button onclick="copyM3U8()">📄 Copiar URL M3U8</button>
        </div>
        
        <div id="message"></div>
        
        <div class="video-container">
            <video
                id="my-video"
                class="video-js vjs-default-skin"
                controls
                preload="auto"
                width="800"
                height="450"
                data-setup="{}">
                <p class="vjs-no-js">
                    Para ver este video necesitas habilitar JavaScript y considerar actualizar a un
                    <a href="https://videojs.com/html5-video-support/" target="_blank">
                        navegador que soporte HTML5 video
                    </a>.
                </p>
            </video>
        </div>
        
        <div class="info">
            <h3>📖 Instrucciones de Uso</h3>
            <ol>
                <li>Pega la URL de un video o stream en vivo de YouTube</li>
                <li>Haz clic en "Cargar Video" para reproducir directamente</li>
                <li>O usa "Generar M3U8" para obtener la URL del stream</li>
                <li>Puedes copiar la URL M3U8 para usar en otros reproductores</li>
            </ol>
        </div>
        
        <div class="examples">
            <h3>🎯 Ejemplos de URLs</h3>
            <div class="example-item">
                <strong>Stream en vivo:</strong><br>
                <code>https://www.youtube.com/watch?v=gOJvu0xYsdo</code>
                <button onclick="loadExample('https://www.youtube.com/watch?v=gOJvu0xYsdo')">Probar</button>
            </div>
            <div class="example-item">
                <strong>Video normal:</strong><br>
                <code>https://www.youtube.com/watch?v=dQw4w9WgXcQ</code>
                <button onclick="loadExample('https://www.youtube.com/watch?v=dQw4w9WgXcQ')">Probar</button>
            </div>
        </div>
    </div>

    <script>
        // Configuración - CAMBIA ESTA URL POR LA DE TU SERVIDOR
        const YUTU_BASE_URL = 'yutu.php';
        
        let player;
        
        // Inicializar Video.js
        document.addEventListener('DOMContentLoaded', function() {
            player = videojs('my-video', {
                fluid: true,
                responsive: true,
                playbackRates: [0.5, 1, 1.25, 1.5, 2],
                plugins: {}
            });
        });
        
        function showMessage(message, type = 'info') {
            const messageDiv = document.getElementById('message');
            messageDiv.className = type;
            messageDiv.innerHTML = message;
        }
        
        function getYouTubeUrl() {
            const url = document.getElementById('youtube-url').value.trim();
            if (!url) {
                showMessage('❌ Por favor ingresa una URL de YouTube', 'error');
                return null;
            }
            
            // Validar URL de YouTube
            const youtubeRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/;
            if (!youtubeRegex.test(url)) {
                showMessage('❌ URL de YouTube no válida', 'error');
                return null;
            }
            
            return url;
        }
        
        function generateM3U8Url(youtubeUrl) {
            return `${YUTU_BASE_URL}?url=${encodeURIComponent(youtubeUrl)}`;
        }
        
        function loadVideo() {
            const youtubeUrl = getYouTubeUrl();
            if (!youtubeUrl) return;
            
            showMessage('🔄 Cargando video...', 'info');
            
            const m3u8Url = generateM3U8Url(youtubeUrl);
            
            player.src({
                src: m3u8Url,
                type: 'application/x-mpegURL'
            });
            
            player.ready(() => {
                player.play().then(() => {
                    showMessage('✅ Video cargado exitosamente', 'success');
                }).catch((error) => {
                    showMessage(`❌ Error al reproducir: ${error.message}`, 'error');
                });
            });
        }
        
        function generateM3U8() {
            const youtubeUrl = getYouTubeUrl();
            if (!youtubeUrl) return;
            
            const m3u8Url = generateM3U8Url(youtubeUrl);
            
            showMessage(`
                ✅ URL M3U8 generada:<br>
                <div class="url-display">${m3u8Url}</div>
                <button onclick="copyToClipboard('${m3u8Url}')">📋 Copiar</button>
            `, 'success');
        }
        
        function copyM3U8() {
            const youtubeUrl = getYouTubeUrl();
            if (!youtubeUrl) return;
            
            const m3u8Url = generateM3U8Url(youtubeUrl);
            copyToClipboard(m3u8Url);
        }
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showMessage('📋 URL copiada al portapapeles', 'success');
            }).catch(() => {
                // Fallback para navegadores que no soportan clipboard API
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showMessage('📋 URL copiada al portapapeles', 'success');
            });
        }
        
        function loadExample(url) {
            document.getElementById('youtube-url').value = url;
            loadVideo();
        }
    </script>
</body>
</html>
